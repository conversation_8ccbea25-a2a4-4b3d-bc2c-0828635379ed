/**
 * Whereby API helper functions for classroom integration
 */

/**
 * Create a new Whereby room for a classroom session
 * @param {Object} params - Room creation parameters
 * @param {string} params.lessonId - The lesson ID
 * @param {string} params.teacherId - The teacher ID
 * @param {string} params.studentId - The student ID
 * @param {boolean} params.isRecurring - Whether this is a recurring lesson
 * @returns {Promise<Object>} Room information
 */
export async function createWherebyRoom({
  lessonId,
  teacherId,
  studentId,
  isRecurring = false,
}) {
  try {
    const response = await fetch('/api/whereby/create-room', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        lessonId,
        teacherId,
        studentId,
        isRecurring,
      }),
    })

    if (!response.ok) {
      let errorMessage = 'Failed to create room'
      try {
        const errorData = await response.json()
        errorMessage = errorData.error || errorMessage
      } catch (parseError) {
        // If we can't parse JSON, it might be HTML (deployment issue)
        const responseText = await response.text()
        if (responseText.includes('<!DOCTYPE')) {
          errorMessage = 'API endpoint not deployed - server middleware not found'
        } else {
          errorMessage = `Server error (${response.status}): ${responseText}`
        }
      }
      throw new Error(errorMessage)
    }

    const data = await response.json()
    return data.room
  } catch (error) {
    console.error('Error creating Whereby room:', error)
    throw error
  }
}

/**
 * Get room information for a lesson
 * @param {string} lessonId - The lesson ID
 * @returns {Promise<Object>} Room information
 */
export async function getWherebyRoom(lessonId) {
  try {
    const response = await fetch(`/api/whereby/room/${lessonId}`)

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || 'Failed to get room info')
    }

    const data = await response.json()
    return data
  } catch (error) {
    console.error('Error fetching Whereby room:', error)
    throw error
  }
}

/**
 * End a Whereby room
 * @param {string} meetingId - The Whereby meeting ID
 * @returns {Promise<Object>} Success response
 */
export async function endWherebyRoom(meetingId) {
  try {
    const response = await fetch(`/api/whereby/room/${meetingId}`, {
      method: 'DELETE',
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || 'Failed to end room')
    }

    const data = await response.json()
    return data
  } catch (error) {
    console.error('Error ending Whereby room:', error)
    throw error
  }
}

/**
 * Generate room URLs with parameters for embedding
 * @param {Object} room - Room information from createWherebyRoom
 * @param {string} role - 'host' or 'participant'
 * @param {Object} options - Additional options
 * @returns {Object} URLs and parameters
 */
export function generateWherebyUrls(room, role = 'participant', options = {}) {
  const {
    displayName = 'User',
    audio = 'on',
    video = 'on',
    chat = 'on',
    people = 'on',
    screenshare = 'on',
    reactions = 'on',
    handRaise = 'on',
    leaveButton = 'on',
    background = 'on',
    recording = 'on',
    breakoutRooms = 'on',
    whiteboard = 'on',
    minimal = 'false',
  } = options

  // Use appropriate URL based on role
  const baseUrl = role === 'host' ? room.hostRoomUrl : room.roomUrl

  // Build embed parameters with all paid features enabled
  const embedParams = new URLSearchParams({
    displayName,
    audio,
    video,
    chat,
    people,
    screenshare,
    reactions,
    handRaise,
    leaveButton,
    background,
    recording,
    breakoutRooms,
    whiteboard,
    minimal,
  })

  // Determine if we need to add & or ? for parameters
  const separator = baseUrl.includes('?') ? '&' : '?'
  const fullUrl = `${baseUrl}${separator}${embedParams.toString()}`

  return {
    baseUrl,
    fullUrl,
    embedParams: embedParams.toString(),
    role,
    meetingId: room.meetingId,
  }
}

/**
 * Open Whereby room in a new window
 * @param {Object} room - Room information
 * @param {string} role - 'host' or 'participant'
 * @param {Object} options - Display options
 * @returns {Window|null} Reference to opened window
 */
export function openWherebyWindow(room, role = 'participant', options = {}) {
  const {
    displayName = 'User',
    windowFeatures = 'width=1200,height=800,scrollbars=yes,resizable=yes,toolbar=no,menubar=no,location=no,status=no',
  } = options

  try {
    const { fullUrl } = generateWherebyUrls(room, role, {
      displayName,
      ...options,
    })

    const windowName = `whereby-${room.lessonId}-${role}`
    const newWindow = window.open(fullUrl, windowName, windowFeatures)

    if (newWindow) {
      newWindow.focus()
      return newWindow
    } else {
      throw new Error('Failed to open window - popup blocked?')
    }
  } catch (error) {
    console.error('Error opening Whereby window:', error)
    throw error
  }
}

/**
 * Check if a room is still active
 * @param {Object} room - Room information
 * @returns {boolean} Whether the room is still active
 */
export function isRoomActive(room) {
  if (!room || !room.endDate) return false

  const now = new Date()
  const endDate = new Date(room.endDate)

  return now < endDate
}

/**
 * Get time remaining for a room
 * @param {Object} room - Room information
 * @returns {number} Minutes remaining (0 if expired)
 */
export function getRoomTimeRemaining(room) {
  if (!room || !room.endDate) return 0

  const now = new Date()
  const endDate = new Date(room.endDate)

  if (now >= endDate) return 0

  const diffMs = endDate - now
  return Math.floor(diffMs / (1000 * 60)) // Convert to minutes
}
